import * as vscode from 'vscode';
import * as path from 'path';
import { OllamaAgentProvider } from './ollamaAgentProvider';
import { OllamaWebviewPanel } from './ollamaWebviewPanel';

let agentProvider: OllamaAgentProvider;
let webviewPanel: OllamaWebviewPanel | undefined;

export function activate(context: vscode.ExtensionContext) {
    console.log('Ollama AI Agent extension is now active!');

    // Initialize the agent provider
    agentProvider = new OllamaAgentProvider(context);

    // Register commands
    const startChatCommand = vscode.commands.registerCommand('ollamaAgent.startChat', async () => {
        if (!webviewPanel) {
            webviewPanel = new OllamaWebviewPanel(context, agentProvider);
        }
        webviewPanel.show();
    });

    const sendMessageCommand = vscode.commands.registerCommand('ollamaAgent.sendMessage', async () => {
        const message = await vscode.window.showInputBox({
            prompt: 'Enter your message for the Ollama AI agent',
            placeHolder: 'Type your message here...'
        });

        if (message) {
            if (!webviewPanel) {
                webviewPanel = new OllamaWebviewPanel(context, agentProvider);
            }
            webviewPanel.show();
            webviewPanel.sendMessage(message);
        }
    });

    const togglePanelCommand = vscode.commands.registerCommand('ollamaAgent.togglePanel', () => {
        if (webviewPanel) {
            webviewPanel.toggle();
        } else {
            vscode.commands.executeCommand('ollamaAgent.startChat');
        }
    });

    // Register tree data provider
    const treeDataProvider = new OllamaTreeDataProvider();
    vscode.window.createTreeView('ollamaAgent', {
        treeDataProvider: treeDataProvider,
        showCollapseAll: true
    });

    // Set context for when extension is enabled
    vscode.commands.executeCommand('setContext', 'ollamaAgent.enabled', true);

    // Auto-start if configured
    const config = vscode.workspace.getConfiguration('ollamaAgent');
    if (config.get('autoStart', true)) {
        agentProvider.startServer();
    }

    // Add to subscriptions
    context.subscriptions.push(
        startChatCommand,
        sendMessageCommand,
        togglePanelCommand,
        agentProvider
    );

    // Show welcome message
    vscode.window.showInformationMessage('Ollama AI Agent is ready! Use Ctrl+Shift+O to start chatting.');
}

export function deactivate() {
    if (webviewPanel) {
        webviewPanel.dispose();
    }
    if (agentProvider) {
        agentProvider.dispose();
    }
}

class OllamaTreeDataProvider implements vscode.TreeDataProvider<OllamaTreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<OllamaTreeItem | undefined | null | void> = new vscode.EventEmitter<OllamaTreeItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<OllamaTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

    getTreeItem(element: OllamaTreeItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: OllamaTreeItem): Thenable<OllamaTreeItem[]> {
        if (!element) {
            return Promise.resolve([
                new OllamaTreeItem('Start Chat', vscode.TreeItemCollapsibleState.None, 'ollamaAgent.startChat'),
                new OllamaTreeItem('Send Message', vscode.TreeItemCollapsibleState.None, 'ollamaAgent.sendMessage'),
                new OllamaTreeItem('Server Status', vscode.TreeItemCollapsibleState.None)
            ]);
        }
        return Promise.resolve([]);
    }

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }
}

class OllamaTreeItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly command?: string
    ) {
        super(label, collapsibleState);
        
        if (command) {
            this.command = {
                command: command,
                title: label
            };
        }

        this.tooltip = `${this.label}`;
        this.contextValue = 'ollamaItem';
    }
}
