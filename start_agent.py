#!/usr/bin/env python3
"""
Standalone script to start the Ollama AI Agent server
This can be used independently of the VS Code extension
"""

import subprocess
import sys
import os
import time
import requests
from dotenv import load_dotenv

def check_dependencies():
    """Check if required Python packages are installed"""
    required_packages = [
        'paramiko', 'python-dotenv', 'fastapi', 
        'uvicorn', 'websockets', 'pydantic'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"Missing required packages: {', '.join(missing_packages)}")
        print("Please install them using: pip install -r requirements.txt")
        return False
    
    return True

def check_server_running(host='localhost', port=8080):
    """Check if the server is already running"""
    try:
        response = requests.get(f'http://{host}:{port}/', timeout=5)
        return response.status_code == 200
    except:
        return False

def start_server():
    """Start the AI agent server"""
    # Load environment variables
    load_dotenv()
    
    host = os.getenv('AGENT_HOST', 'localhost')
    port = int(os.getenv('AGENT_PORT', 8080))
    
    print("🤖 Ollama AI Agent Server")
    print("=" * 30)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check if server is already running
    if check_server_running(host, port):
        print(f"✅ Server is already running at http://{host}:{port}")
        print("You can now use the VS Code extension or make direct API calls.")
        return
    
    # Display configuration
    ssh_host = os.getenv('SSH_HOST', 'Not configured')
    ssh_user = os.getenv('SSH_USER', 'Not configured')
    ollama_model = os.getenv('OLLAMA_MODEL', 'Not configured')
    
    print(f"SSH Target: {ssh_user}@{ssh_host}")
    print(f"Ollama Model: {ollama_model}")
    print(f"Server: http://{host}:{port}")
    print()
    
    # Start the server
    try:
        print("🚀 Starting server...")
        subprocess.run([sys.executable, 'ai_agent.py'], check=True)
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Server failed to start: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print("❌ ai_agent.py not found. Make sure you're in the correct directory.")
        sys.exit(1)

if __name__ == "__main__":
    start_server()
